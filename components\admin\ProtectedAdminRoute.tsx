"use client"

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/lib/auth-context'
import { Loader2 } from 'lucide-react'

interface ProtectedAdminRouteProps {
  children: React.ReactNode
  requiredPermission?: string
  adminOnly?: boolean
}

export default function ProtectedAdminRoute({ 
  children, 
  requiredPermission,
  adminOnly = false 
}: ProtectedAdminRouteProps) {
  const { user, loading, hasPermission, isAdmin, isEmployee } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (loading) return

    // Not authenticated
    if (!user) {
      router.push('/admin/login')
      return
    }

    // Not admin or employee
    if (!isAdmin && !isEmployee) {
      router.push('/admin/login')
      return
    }

    // Admin only route but user is not admin
    if (adminOnly && !isAdmin) {
      router.push('/admin/dashboard') // Redirect to dashboard with error
      return
    }

    // Check specific permission if required
    if (requiredPermission && !hasPermission(requiredPermission)) {
      router.push('/admin/dashboard') // Redirect to dashboard with error
      return
    }
  }, [user, loading, router, hasPermission, isAdmin, isEmployee, requiredPermission, adminOnly])

  // Show loading while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-gray-600">Vérification des autorisations...</span>
        </div>
      </div>
    )
  }

  // Not authenticated or insufficient permissions
  if (!user || (!isAdmin && !isEmployee)) {
    return null // Will redirect in useEffect
  }

  // Admin only check
  if (adminOnly && !isAdmin) {
    return null // Will redirect in useEffect
  }

  // Permission check
  if (requiredPermission && !hasPermission(requiredPermission)) {
    return null // Will redirect in useEffect
  }

  return <>{children}</>
}

// Higher-order component for admin-only pages
export function withAdminAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredPermission?: string,
  adminOnly?: boolean
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedAdminRoute 
        requiredPermission={requiredPermission}
        adminOnly={adminOnly}
      >
        <Component {...props} />
      </ProtectedAdminRoute>
    )
  }
}
