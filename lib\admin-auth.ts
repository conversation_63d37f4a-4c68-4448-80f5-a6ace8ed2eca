import { NextRequest } from 'next/server'
import { supabaseAdmin } from './supabase'
import { Database } from './types/database'

export type AdminUser = Database['public']['Tables']['profiles']['Row'] & {
  permissions?: string[]
}

export interface AdminAuthResult {
  success: boolean
  user?: AdminUser
  error?: string
}

/**
 * Verify admin authentication and authorization
 */
export async function verifyAdminAuth(request: NextRequest): Promise<AdminAuthResult> {
  try {
    // Get authorization header
    const authHeader = request.headers.get('authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return { success: false, error: 'Missing or invalid authorization header' }
    }

    const token = authHeader.substring(7)

    // Verify JWT token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      return { success: false, error: 'Invalid or expired token' }
    }

    // Get user profile with role information
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return { success: false, error: 'User profile not found' }
    }

    // Check if user has admin or employee role
    if (!['admin', 'employee'].includes(profile.role)) {
      return { success: false, error: 'Insufficient permissions' }
    }

    return {
      success: true,
      user: {
        ...profile,
        permissions: getPermissionsForRole(profile.role)
      }
    }
  } catch (error) {
    console.error('Admin auth verification error:', error)
    return { success: false, error: 'Authentication verification failed' }
  }
}

/**
 * Get permissions based on user role
 */
function getPermissionsForRole(role: string): string[] {
  switch (role) {
    case 'admin':
      return [
        'services:read', 'services:write', 'services:delete',
        'reservations:read', 'reservations:write', 'reservations:delete',
        'customers:read', 'customers:write', 'customers:delete',
        'employees:read', 'employees:write', 'employees:delete',
        'equipment:read', 'equipment:write', 'equipment:delete',
        'analytics:read', 'settings:write', 'users:manage'
      ]
    case 'employee':
      return [
        'services:read',
        'reservations:read', 'reservations:write',
        'customers:read', 'customers:write',
        'equipment:read'
      ]
    default:
      return []
  }
}

/**
 * Check if user has specific permission
 */
export function hasPermission(user: AdminUser, permission: string): boolean {
  return user.permissions?.includes(permission) || false
}

/**
 * Middleware wrapper for admin routes
 */
export function withAdminAuth(
  handler: (request: NextRequest, user: AdminUser) => Promise<Response>,
  requiredPermission?: string
) {
  return async (request: NextRequest) => {
    const authResult = await verifyAdminAuth(request)
    
    if (!authResult.success || !authResult.user) {
      return new Response(
        JSON.stringify({ error: authResult.error || 'Authentication failed' }),
        { status: 401, headers: { 'Content-Type': 'application/json' } }
      )
    }

    // Check specific permission if required
    if (requiredPermission && !hasPermission(authResult.user, requiredPermission)) {
      return new Response(
        JSON.stringify({ error: 'Insufficient permissions' }),
        { status: 403, headers: { 'Content-Type': 'application/json' } }
      )
    }

    return handler(request, authResult.user)
  }
}

/**
 * Log admin action for audit trail
 */
export async function logAdminAction(
  adminUserId: string,
  action: string,
  tableName?: string,
  recordId?: string,
  oldValues?: any,
  newValues?: any,
  request?: NextRequest
) {
  try {
    const auditData = {
      admin_user_id: adminUserId,
      action,
      table_name: tableName,
      record_id: recordId,
      old_values: oldValues,
      new_values: newValues,
      ip_address: request?.ip || request?.headers.get('x-forwarded-for') || null,
      user_agent: request?.headers.get('user-agent') || null,
      session_id: null // Could be extracted from token if needed
    }

    const { error } = await supabaseAdmin
      .from('admin_audit_log')
      .insert(auditData)

    if (error) {
      console.error('Failed to log admin action:', error)
    }
  } catch (error) {
    console.error('Error logging admin action:', error)
  }
}
