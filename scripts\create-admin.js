#!/usr/bin/env node

// Simple admin creation script using existing dependencies
// Run with: node scripts/create-admin.js

const { createClient } = require("@supabase/supabase-js");

// Replace these with your actual values from .env.local
const SUPABASE_URL = "https://zalzjvuxoffmhaokvzda.supabase.co";
const SUPABASE_SERVICE_KEY =
	"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InphbHpqdnV4b2ZmbWhhb2t2emRhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjYyMDU2MywiZXhwIjoyMDY4MTk2NTYzfQ.3UyRaKx1aoBTDQXJazne99UaoXVa5IfKK5S_oCkwtVI";

const readline = require("readline");

// Function to prompt for user input
function askQuestion(question) {
	const rl = readline.createInterface({
		input: process.stdin,
		output: process.stdout,
	});

	return new Promise((resolve) => {
		rl.question(question, (answer) => {
			rl.close();
			resolve(answer);
		});
	});
}

async function createAdmin() {
	console.log("🚀 Creating admin user...");
	console.log("");

	// Prompt for admin details
	const ADMIN_EMAIL =
		(await askQuestion("Enter admin email (default: <EMAIL>): ")) ||
		"<EMAIL>";
	const ADMIN_PASSWORD = (await askQuestion("Enter admin password (default: admin123): ")) || "admin123";
	const FIRST_NAME = (await askQuestion("Enter first name (default: Admin): ")) || "Admin";
	const LAST_NAME = (await askQuestion("Enter last name (default: User): ")) || "User";

	console.log("");
	console.log(`Creating admin user: ${FIRST_NAME} ${LAST_NAME} (${ADMIN_EMAIL})`);
	console.log("");

	try {
		// Create Supabase client with service role
		const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
			auth: {
				autoRefreshToken: false,
				persistSession: false,
			},
		});

		console.log("📧 Creating auth user...");

		// Create user in Supabase Auth
		const { data: authData, error: authError } = await supabase.auth.admin.createUser({
			email: ADMIN_EMAIL,
			password: ADMIN_PASSWORD,
			email_confirm: true,
			user_metadata: {
				first_name: FIRST_NAME,
				last_name: LAST_NAME,
				role: "admin",
			},
		});

		if (authError) {
			console.error("❌ Auth error:", authError.message);
			process.exit(1);
		}

		if (!authData.user) {
			console.error("❌ Failed to create user");
			process.exit(1);
		}

		console.log("👤 Creating profile...");

		// Create profile record
		const { error: profileError } = await supabase.from("profiles").insert({
			id: authData.user.id,
			email: ADMIN_EMAIL,
			first_name: FIRST_NAME,
			last_name: LAST_NAME,
			role: "admin",
		});

		if (profileError) {
			console.error("❌ Profile error:", profileError.message);
			// Try to clean up
			await supabase.auth.admin.deleteUser(authData.user.id);
			process.exit(1);
		}

		console.log("✅ Admin user created successfully!");
		console.log("");
		console.log("📋 Login Details:");
		console.log(`   Email: ${ADMIN_EMAIL}`);
		console.log(`   Password: ${ADMIN_PASSWORD}`);
		console.log(`   Role: admin`);
		console.log("");
		console.log("🌐 You can now login at: http://localhost:3000/login");
	} catch (error) {
		console.error("❌ Unexpected error:", error);
		process.exit(1);
	}
}

// Run the script
createAdmin().catch(console.error);
