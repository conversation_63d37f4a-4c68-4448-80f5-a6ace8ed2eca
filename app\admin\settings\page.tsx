"use client"

import { useState } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import { useSettings } from '@/lib/use-settings'
import { Save, Loader2, Settings, Info } from 'lucide-react'

export default function AdminSettingsPage() {
  const { categorized, loading, error, updateSetting } = useSettings()
  const [saving, setSaving] = useState<string | null>(null)
  const [localValues, setLocalValues] = useState<Record<string, any>>({})

  const handleValueChange = (key: string, value: any) => {
    setLocalValues(prev => ({ ...prev, [key]: value }))
  }

  const handleSave = async (key: string, value: any) => {
    setSaving(key)
    try {
      const success = await updateSetting(key, value)
      if (success) {
        // Remove from local values since it's now saved
        setLocalValues(prev => {
          const newValues = { ...prev }
          delete newValues[key]
          return newValues
        })
      }
    } finally {
      setSaving(null)
    }
  }

  const getValue = (key: string, originalValue: any) => {
    return localValues.hasOwnProperty(key) ? localValues[key] : originalValue
  }

  const hasChanges = (key: string) => {
    return localValues.hasOwnProperty(key)
  }

  if (loading) {
    return (
      <AdminLayout adminOnly>
        <div className="flex items-center justify-center h-64">
          <div className="flex items-center space-x-2">
            <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-gray-600">Chargement des paramètres...</span>
          </div>
        </div>
      </AdminLayout>
    )
  }

  if (error) {
    return (
      <AdminLayout adminOnly>
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <Info className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Erreur</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      </AdminLayout>
    )
  }

  return (
    <AdminLayout adminOnly>
      <div className="space-y-6">
        <div className="flex items-center space-x-3">
          <Settings className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Paramètres</h1>
            <p className="text-gray-600">Gérer la configuration de l'entreprise</p>
          </div>
        </div>

        {Object.entries(categorized).map(([category, settings]) => (
          <div key={category} className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900 capitalize">
                {category.replace('_', ' ')}
              </h2>
            </div>
            <div className="p-6 space-y-4">
              {Object.entries(settings).map(([key, setting]) => (
                <div key={key} className="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    {setting.description && (
                      <p className="text-xs text-gray-500 mt-1">{setting.description}</p>
                    )}
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-xs px-2 py-1 bg-gray-100 rounded">
                        {setting.type}
                      </span>
                      {setting.isPublic && (
                        <span className="text-xs px-2 py-1 bg-green-100 text-green-800 rounded">
                          Public
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    {setting.type === 'boolean' ? (
                      <select
                        value={getValue(key, setting.value).toString()}
                        onChange={(e) => handleValueChange(key, e.target.value === 'true')}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      >
                        <option value="true">Oui</option>
                        <option value="false">Non</option>
                      </select>
                    ) : setting.type === 'json' ? (
                      <textarea
                        value={JSON.stringify(getValue(key, setting.value), null, 2)}
                        onChange={(e) => {
                          try {
                            const parsed = JSON.parse(e.target.value)
                            handleValueChange(key, parsed)
                          } catch {
                            // Invalid JSON, keep as string for now
                            handleValueChange(key, e.target.value)
                          }
                        }}
                        rows={3}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    ) : setting.type === 'number' ? (
                      <input
                        type="number"
                        value={getValue(key, setting.value)}
                        onChange={(e) => handleValueChange(key, parseFloat(e.target.value) || 0)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    ) : (
                      <input
                        type="text"
                        value={getValue(key, setting.value)}
                        onChange={(e) => handleValueChange(key, e.target.value)}
                        className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm"
                      />
                    )}
                  </div>
                  
                  <div>
                    {hasChanges(key) && (
                      <button
                        onClick={() => handleSave(key, localValues[key])}
                        disabled={saving === key}
                        className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
                      >
                        {saving === key ? (
                          <Loader2 className="h-4 w-4 animate-spin" />
                        ) : (
                          <Save className="h-4 w-4" />
                        )}
                        <span className="ml-2">Sauvegarder</span>
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </AdminLayout>
  )
}
