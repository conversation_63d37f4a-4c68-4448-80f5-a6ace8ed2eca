"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { User, Session, AuthError } from '@supabase/supabase-js'
import { supabase } from './supabase'
import { Database } from './types/database'

type Profile = Database['public']['Tables']['profiles']['Row']

export interface AuthUser extends User {
  profile?: Profile
  permissions?: string[]
}

interface AuthContextType {
  user: AuthUser | null
  session: Session | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>
  signOut: () => Promise<{ error: AuthError | null }>
  refreshSession: () => Promise<void>
  hasPermission: (permission: string) => boolean
  isAdmin: boolean
  isEmployee: boolean
  isCustomer: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

function getPermissionsForRole(role: string): string[] {
  switch (role) {
    case 'admin':
      return [
        'services:read', 'services:write', 'services:delete',
        'reservations:read', 'reservations:write', 'reservations:delete',
        'customers:read', 'customers:write', 'customers:delete',
        'employees:read', 'employees:write', 'employees:delete',
        'equipment:read', 'equipment:write', 'equipment:delete',
        'analytics:read', 'settings:write', 'users:manage'
      ]
    case 'employee':
      return [
        'services:read',
        'reservations:read', 'reservations:write',
        'customers:read', 'customers:write',
        'equipment:read'
      ]
    case 'customer':
      return [
        'reservations:read', 'reservations:write',
        'profile:read', 'profile:write'
      ]
    default:
      return []
  }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [session, setSession] = useState<Session | null>(null)
  const [loading, setLoading] = useState(true)

  const fetchUserProfile = async (userId: string): Promise<Profile | null> => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single()

      if (error) {
        console.error('Error fetching user profile:', error)
        return null
      }

      return profile
    } catch (error) {
      console.error('Error fetching user profile:', error)
      return null
    }
  }

  const updateUserWithProfile = async (authUser: User | null) => {
    if (!authUser) {
      setUser(null)
      return
    }

    const profile = await fetchUserProfile(authUser.id)
    const permissions = profile ? getPermissionsForRole(profile.role) : []

    setUser({
      ...authUser,
      profile,
      permissions
    })
  }

  const signIn = async (email: string, password: string) => {
    setLoading(true)
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        setLoading(false)
        return { error }
      }

      if (data.user) {
        await updateUserWithProfile(data.user)
      }

      setLoading(false)
      return { error: null }
    } catch (error) {
      setLoading(false)
      return { error: error as AuthError }
    }
  }

  const signOut = async () => {
    setLoading(true)
    try {
      const { error } = await supabase.auth.signOut()
      
      if (!error) {
        setUser(null)
        setSession(null)
      }
      
      setLoading(false)
      return { error }
    } catch (error) {
      setLoading(false)
      return { error: error as AuthError }
    }
  }

  const refreshSession = async () => {
    try {
      const { data: { session }, error } = await supabase.auth.refreshSession()
      
      if (error) {
        console.error('Error refreshing session:', error)
        return
      }

      if (session?.user) {
        await updateUserWithProfile(session.user)
        setSession(session)
      }
    } catch (error) {
      console.error('Error refreshing session:', error)
    }
  }

  const hasPermission = (permission: string): boolean => {
    return user?.permissions?.includes(permission) || false
  }

  const isAdmin = user?.profile?.role === 'admin'
  const isEmployee = user?.profile?.role === 'employee'
  const isCustomer = user?.profile?.role === 'customer'

  useEffect(() => {
    // Get initial session
    const getInitialSession = async () => {
      const { data: { session }, error } = await supabase.auth.getSession()
      
      if (error) {
        console.error('Error getting session:', error)
        setLoading(false)
        return
      }

      setSession(session)
      
      if (session?.user) {
        await updateUserWithProfile(session.user)
      }
      
      setLoading(false)
    }

    getInitialSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)
        
        setSession(session)
        
        if (session?.user) {
          await updateUserWithProfile(session.user)
        } else {
          setUser(null)
        }
        
        setLoading(false)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const value: AuthContextType = {
    user,
    session,
    loading,
    signIn,
    signOut,
    refreshSession,
    hasPermission,
    isAdmin,
    isEmployee,
    isCustomer
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Hook for admin-only access
export function useAdminAuth() {
  const auth = useAuth()
  
  if (!auth.isAdmin && !auth.isEmployee) {
    throw new Error('Admin or employee access required')
  }
  
  return auth
}

// Hook for customer access
export function useCustomerAuth() {
  const auth = useAuth()
  
  if (!auth.isCustomer) {
    throw new Error('Customer access required')
  }
  
  return auth
}
