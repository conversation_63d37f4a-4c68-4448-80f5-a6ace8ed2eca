import { NextRequest, NextResponse } from 'next/server'
import { supabaseAdmin } from '@/lib/supabase'
import { verifyAdminAuth, logAdminAction } from '@/lib/admin-auth'

export async function POST(request: NextRequest) {
  try {
    // Verify the user is authenticated before logging out
    const authResult = await verifyAdminAuth(request)
    
    if (authResult.success && authResult.user) {
      // Log the logout action
      await logAdminAction(
        authResult.user.id,
        'LOGOUT',
        'auth',
        null,
        null,
        {
          email: authResult.user.email,
          role: authResult.user.role,
          logoutTime: new Date().toISOString()
        },
        request
      )
    }

    // Get the authorization header to extract the token
    const authHeader = request.headers.get('authorization')
    if (authHeader && authHeader.startsWith('Bearer ')) {
      const token = authHeader.substring(7)
      
      // Sign out the user session
      const { error } = await supabaseAdmin.auth.admin.signOut(token)
      
      if (error) {
        console.error('Error signing out user:', error)
        // Don't return error to client as logout should always succeed from client perspective
      }
    }

    return NextResponse.json({
      message: 'Logged out successfully'
    })

  } catch (error) {
    console.error('Admin logout error:', error)
    // Always return success for logout to prevent client-side issues
    return NextResponse.json({
      message: 'Logged out successfully'
    })
  }
}
