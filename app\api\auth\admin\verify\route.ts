import { NextRequest, NextResponse } from 'next/server'
import { verifyAdminAuth } from '@/lib/admin-auth'

export async function GET(request: NextRequest) {
  try {
    const authResult = await verifyAdminAuth(request)
    
    if (!authResult.success || !authResult.user) {
      return NextResponse.json(
        { error: authResult.error || 'Authentication failed' },
        { status: 401 }
      )
    }

    // Return user data if authentication is valid
    return NextResponse.json({
      user: {
        id: authResult.user.id,
        email: authResult.user.email,
        role: authResult.user.role,
        permissions: authResult.user.permissions,
        profile: {
          first_name: authResult.user.first_name,
          last_name: authResult.user.last_name,
          phone: authResult.user.phone
        }
      },
      authenticated: true
    })

  } catch (error) {
    console.error('Admin verify error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
